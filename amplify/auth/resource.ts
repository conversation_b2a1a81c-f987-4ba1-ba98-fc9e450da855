import { defineAuth } from '@aws-amplify/backend';

/**
 * Define and configure a Cognito User Pool with Email & Password login
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
  },
  userAttributes: {
    fullName: {
      mutable: true,
      required: true,
    },
    email: {
      mutable: true,
      required: true,
    },
  },
  // Optional: Configure password policy
  passwordPolicy: {
    minLength: 8,
    requireLowercase: true,
    requireUppercase: true,
    requireNumbers: true,
    requireSymbols: false,
  },
  // Optional: Configure account recovery
  accountRecovery: 'EMAIL_ONLY',
  // Optional: Configure multi-factor authentication
  multifactor: {
    mode: 'OPTIONAL',
    totp: true,
    sms: false,
  },
});
