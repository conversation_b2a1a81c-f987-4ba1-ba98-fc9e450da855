{"version": "1", "auth": {"user_pool_id": "PLACEHOLDER_USER_POOL_ID", "aws_region": "us-east-1", "user_pool_client_id": "PLACEHOLDER_CLIENT_ID", "identity_pool_id": "PLACEHOLDER_IDENTITY_POOL_ID", "mfa_methods": [], "standard_required_attributes": ["email"], "username_attributes": ["email"], "user_verification_types": ["email"], "mfa_configuration": "OFF", "password_policy": {"min_length": 8, "require_numbers": true, "require_lowercase": true, "require_uppercase": true, "require_symbols": false}}}