
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { Company } from '@/types/onboarding';

export interface DatabaseCompany {
  id: string;
  name: string;
  industry: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export function useCompany() {
  const { user } = useAuth();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchCompany();
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchCompany = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Fetching company for user:', user?.userId);

      // First get user's profile to find company_id
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user?.userId)
        .maybeSingle();

      if (profileError) {
        console.error('Profile fetch error:', profileError);
        throw profileError;
      }

      console.log('User profile:', profile);

      if (profile?.company_id) {
        // Fetch company details
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('*')
          .eq('id', profile.company_id)
          .maybeSingle();

        if (companyError) {
          console.error('Company fetch error:', companyError);
          throw companyError;
        }

        console.log('Company data:', companyData);

        if (companyData) {
          setCompany({
            id: companyData.id,
            name: companyData.name,
            industry: companyData.industry as Company['industry'],
            createdBy: companyData.created_by || '',
            createdAt: new Date(companyData.created_at)
          });
        }
      }
    } catch (err) {
      console.error('Error fetching company:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch company');
    } finally {
      setLoading(false);
    }
  };

  const createCompany = async (companyData: Omit<Company, 'id' | 'createdBy' | 'createdAt'>) => {
    try {
      if (!user) throw new Error('User not authenticated');

      console.log('Creating company with data:', companyData);
      console.log('Current user:', user.userId);

      // Create company
      const { data: newCompany, error: companyError } = await supabase
        .from('companies')
        .insert({
          name: companyData.name,
          industry: companyData.industry,
          created_by: user.userId
        })
        .select()
        .single();

      if (companyError) {
        console.error('Company creation error:', companyError);
        throw companyError;
      }

      console.log('Created company:', newCompany);

      // Update user profile with company_id and role
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          company_id: newCompany.id,
          role: 'CEO'
        })
        .eq('id', user.userId);

      if (profileError) {
        console.error('Profile update error:', profileError);
        throw profileError;
      }

      console.log('Updated user profile with company_id');

      const company: Company = {
        id: newCompany.id,
        name: newCompany.name,
        industry: newCompany.industry as Company['industry'],
        createdBy: newCompany.created_by || '',
        createdAt: new Date(newCompany.created_at)
      };

      setCompany(company);
      return company;
    } catch (err) {
      console.error('Error creating company:', err);
      throw err;
    }
  };

  return {
    company,
    loading,
    error,
    createCompany,
    refetch: fetchCompany
  };
}
