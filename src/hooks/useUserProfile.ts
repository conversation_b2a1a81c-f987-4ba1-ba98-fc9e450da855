
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { User } from '@/types/onboarding';

export function useUserProfile() {
  const { user: authUser } = useAuth();
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (authUser) {
      fetchProfile();
    } else {
      setLoading(false);
    }
  }, [authUser]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Fetching profile for user:', authUser?.userId);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authUser?.userId)
        .maybeSingle();

      if (error) {
        console.error('Profile fetch error:', error);
        throw error;
      }

      console.log('Fetched profile:', data);

      if (data) {
        setProfile({
          id: data.id,
          email: authUser?.username || '',
          fullName: data.full_name || '',
          role: data.role as User['role'] || 'Business Admin',
          companyId: data.company_id || undefined,
          invitedBy: data.invited_by || undefined,
          hasCompletedOnboarding: data.has_completed_onboarding || false,
          onboardingStep: data.onboarding_step || 1,
          smsAlerts: data.sms_alerts ?? true,
          emailAlerts: data.email_alerts ?? true
        });
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch profile');
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      if (!authUser) throw new Error('User not authenticated');

      console.log('Updating profile with:', updates);

      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: updates.fullName,
          role: updates.role,
          sms_alerts: updates.smsAlerts,
          email_alerts: updates.emailAlerts,
          has_completed_onboarding: updates.hasCompletedOnboarding,
          onboarding_step: updates.onboardingStep
        })
        .eq('id', authUser.userId);

      if (error) {
        console.error('Profile update error:', error);
        throw error;
      }

      console.log('Profile updated successfully');
      await fetchProfile();
    } catch (err) {
      console.error('Error updating profile:', err);
      throw err;
    }
  };

  const completeOnboarding = async () => {
    await updateProfile({
      hasCompletedOnboarding: true,
      onboardingStep: 5
    });
  };

  return {
    profile,
    loading,
    error,
    updateProfile,
    completeOnboarding,
    refetch: fetchProfile
  };
}
